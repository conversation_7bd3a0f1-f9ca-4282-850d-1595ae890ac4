using System.Diagnostics;
using Esky.Packages.Application.Abstractions.Pipelines;
using Esky.Packages.Application.Observability;
using Esky.Packages.Application.PackagesGeneration.State;
using KeyValue = System.Collections.Generic.KeyValuePair<string, string>;

namespace Esky.Packages.Application.PackagesGeneration;

public class PackagePipeline(
    PipelineStageBase<PackagePipelineContext> stage, 
    PackagePipelineContext context,
    IPipelineRepository pipelineRepository) 
    : PipelineStageBase<PackagePipelineContext>()
{
    public PackagePipelineContext Context { get; } = context;

    public Task Run(CancellationToken cancellationToken)
        => Run(Context, cancellationToken);

    public override async Task Run(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        var tags = new ActivityTagsCollection
        {
            { "pipelineId", context.PipelineId },
            { "definitionId", context.State.TargetDefinitionId },
            { "pipelineGroupId", context.Options.PipelineGroupId },
            { "startedByUserId", context.Options.StartedByUserId },
            { "partition", context.Options.Partition },
            { "totalPartitions", context.Options.TotalPartitions },
            { "generationMode", context.Options.GenerationMode },
        };

        using var activity = Activities.Source.StartActivity(ActivityKind.Consumer, tags: tags);
        
        using (context.Logger.BeginScope(new KeyValue("pipelineId", context.PipelineId)))
        {
            try
            {
                await pipelineRepository.MarkAsRunning(context.PipelineId, cancellationToken);

                await base.Run(context, cancellationToken);

                await pipelineRepository.MarkAsCompleted(context.PipelineId, cancellationToken);
                activity?.SetStatus(ActivityStatusCode.Ok);
            }
            catch (Exception ex)
            {
                await pipelineRepository.MarkAsFailed(context.PipelineId, ex.Message, cancellationToken);
                activity?.SetStatus(ActivityStatusCode.Error);

                throw;
            }
        }
    }

    protected override async Task RunImpl(PackagePipelineContext context, CancellationToken cancellationToken)
    {
        await stage.Run(context, cancellationToken);
    }
}