using Esky.Packages.Application.Abstractions.Gateways.FlightGateway;
using Esky.Packages.Application.Abstractions.Gateways.HotelsApiGateway;
using Esky.Packages.Application.Dtos.Common;
using Esky.Packages.Application.Dtos.FlightOffers;
using Esky.Packages.Application.Dtos.HotelOffers;
using Esky.Packages.Application.Observability;
using Esky.Packages.Contract.Search;
using Esky.Packages.Domain.Factories;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.Markets;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Model.PackageHotelAirports;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Model.PackageVariants;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Esky.Packages.Domain.Types.Extensions;
using Microsoft.Extensions.Logging;

namespace Esky.Packages.Application.Services;

public record SearchParameters(
    string MarketId,
    List<int> MetaCodes,
    List<int> StayLengths,
    DateOnly Departure<PERSON>ate<PERSON>rom,
    DateOnly DepartureDateTo,
    List<Airport> DepartureAirports,
    List<MealPlan> MealPlans,
    List<Occupancy> Occupancies,
    List<TimeOfDay> InboundDepartures,
    List<TimeOfDay> OutboundDepartures,
    int? MaxPrice,
    bool UseDynamicSearchFallback);

public interface ISearchService
{
    Task<SearchDto> Search(SearchParameters parameters, CancellationToken cancellationToken = default);
}

internal class SearchService(
    IMarketRepository marketRepository,
    IPackageVariantRepository packageVariantRepository,
    IPackageFlightRepository packageFlightRepository,
    IPackageFlightVariantRepository packageFlightVariantRepository,
    IPackageHotelOfferRepository packageHotelOfferRepository,
    IPackageHotelAirportsRepository packageHotelAirportsRepository,
    IPackageFlightFactory packageFlightFactory,
    IPackageHotelOfferFactory packageHotelOfferFactory,
    IFlightGateway flightGateway,
    IHotelOfferLiveGateway hotelOfferLiveGateway,
    ILogger<SearchService> logger)
    : ISearchService
{
    public async Task<SearchDto> Search(SearchParameters parameters, CancellationToken cancellationToken = default)
    {
        var packageVariants = new List<PackageVariantDto>();

        var packageOccupancy = parameters.Occupancies.Count == 1
            ? PackageOccupancy.Map(parameters.Occupancies.Single(), shouldThrow: false)
            : null;

        if (packageOccupancy != null)
        {
            // if (IsVariantSearchSupported(parameters))
            // {
            //     try
            //     {
            //         var packageVariantSearch = await GetPackageVariantSearch(
            //             marketId: parameters.MarketId,
            //             metaCodes: parameters.MetaCodes,
            //             stayLengths: parameters.StayLengths,
            //             departureDateFrom: parameters.DepartureDateFrom,
            //             departureDateTo: parameters.DepartureDateTo,
            //             departureAirports: parameters.DepartureAirports,
            //             mealPlans: parameters.MealPlans,
            //             occupancy: (PackageOccupancy)packageOccupancy!,
            //             cancellationToken);
            //
            //         packageVariants.AddRange(packageVariantSearch.Select(v => Map(v, PackageVariantSourceDto.Variant)));
            //         ApplicationMetrics.RegisterPackageVariantSearchOffers(packageVariantSearch.Count);
            //     }
            //     catch (Exception e)
            //     {
            //         logger.LogWarning(e, "Failed to get package variant search");
            //         // ignore errors from package variant search, we will try to get package factors search
            //     }
            // }

            var missingMetaCodes = parameters.MetaCodes.Except(packageVariants.Select(v => v.MetaCode)).ToList();

            if (missingMetaCodes.Count > 0)
            {
                var packageFactorsSearch = await GetPackageFactorsSearch(
                    marketId: parameters.MarketId,
                    metaCodes: missingMetaCodes,
                    stayLengths: parameters.StayLengths,
                    departureDateFrom: parameters.DepartureDateFrom,
                    departureDateTo: parameters.DepartureDateTo,
                    departureAirports: parameters.DepartureAirports,
                    mealPlans: parameters.MealPlans,
                    occupancy: (PackageOccupancy)packageOccupancy,
                    inboundDepartures: parameters.InboundDepartures,
                    outboundDepartures: parameters.OutboundDepartures,
                    cancellationToken);

                packageVariants.AddRange(packageFactorsSearch.Select(v => Map(v, PackageVariantSourceDto.Factors)));
                ApplicationMetrics.RegisterPackageFactorsSearchOffers(packageFactorsSearch.Count);
            }
        }

        if (packageVariants.Count == 0 && parameters.UseDynamicSearchFallback)
        {
            var packageDynamicSearch = await GetPackageDynamicSearch(
                marketId: parameters.MarketId,
                metaCodes: parameters.MetaCodes,
                stayLengths: parameters.StayLengths,
                departureDateFrom: parameters.DepartureDateFrom,
                departureDateTo: parameters.DepartureDateTo,
                departureAirports: parameters.DepartureAirports,
                mealPlans: parameters.MealPlans,
                occupancies: parameters.Occupancies,
                inboundDepartures: parameters.InboundDepartures,
                outboundDepartures: parameters.OutboundDepartures,
                cancellationToken);

            packageVariants.AddRange(packageDynamicSearch.Select(v => Map(v, PackageVariantSourceDto.Dynamic)));

            ApplicationMetrics.RegisterPackageDynamicSearchOffers(packageVariants.Count);
        }

        if (parameters.MaxPrice != null)
        {
            packageVariants = packageVariants
                .Where(v => v.Price <= parameters.MaxPrice)
                .ToList();
        }

        return new SearchDto(Variants: packageVariants);
    }

    private async Task<List<PackageVariant>> GetPackageVariantSearch(string marketId, List<int> metaCodes,
        List<int> stayLengths, DateOnly departureDateFrom, DateOnly departureDateTo, List<Airport> departureAirports,
        List<MealPlan> mealPlans, PackageOccupancy occupancy, CancellationToken cancellationToken)
    {
        return await packageVariantRepository.GetCheapestsPerMetaCode(
            metaCodes: metaCodes,
            stayLengths: stayLengths,
            marketId: marketId,
            mealPlans: mealPlans,
            occupancy: occupancy,
            departureDateFrom: departureDateFrom,
            departureDateTo: departureDateTo,
            departureAirports: departureAirports,
            cancellationToken);
    }

    private async Task<List<PackageVariant>> GetPackageFactorsSearch(string marketId, List<int> metaCodes,
        List<int> stayLengths, DateOnly departureDateFrom, DateOnly departureDateTo, List<Airport> departureAirports,
        List<MealPlan> mealPlans, PackageOccupancy occupancy, List<TimeOfDay> inboundDepartures,
        List<TimeOfDay> outboundDepartures, CancellationToken cancellationToken)
    {
        var packageHotelAirports = await packageHotelAirportsRepository.ListByMarketIdAndMetaCodes(marketId,
            metaCodes, cancellationToken);
        var arrivalAirports = packageHotelAirports.SelectMany(m => m.Airports).Distinct().ToList();

        if (arrivalAirports.Count == 0)
        {
            return [];
        }

        var packageFlightVariants = await packageFlightVariantRepository.GetCheapestsPerAirports(
            marketId: marketId,
            stayLengths: stayLengths,
            departureAirports: departureAirports,
            arrivalAirports: arrivalAirports,
            departureDateFrom: departureDateFrom,
            departureDateTo: departureDateTo,
            occupancy: occupancy,
            inboundDepartures: inboundDepartures,
            outboundDepartures: outboundDepartures,
            cancellationToken: cancellationToken);

        var packageHotelOfferIds = GetHotelOfferIds(packageFlightVariants, packageHotelAirports);
        if (packageHotelOfferIds.Count == 0)
        {
            return [];
        }

        var packageHotelOffers = await packageHotelOfferRepository.ListByIds(packageHotelOfferIds, cancellationToken);
        if (packageHotelOffers.Count == 0)
        {
            return [];
        }

        var packageHotelOfferVariants = packageHotelOffers
            .SelectMany(h => h.GetVariants([occupancy], mealPlans))
            .ToList();

        return PackageVariant.CreateManyPerMetaCode(packageFlightVariants, packageHotelOfferVariants,
            packageHotelAirports);
    }

    private async Task<List<PackageVariant>> GetPackageDynamicSearch(string marketId, List<int> metaCodes,
        List<int> stayLengths, DateOnly departureDateFrom, DateOnly departureDateTo, List<Airport> departureAirports,
        List<MealPlan> mealPlans, List<Occupancy> occupancies, List<TimeOfDay> inboundDepartures,
        List<TimeOfDay> outboundDepartures, CancellationToken cancellationToken)
    {
        var mergedOccupancy = occupancies.Merge();
        var packageOccupancy = (PackageOccupancy)PackageOccupancy.MapExact(mergedOccupancy, shouldThrow: false)!;

        var market = await marketRepository.GetById(marketId, cancellationToken);
        if (market == null)
        {
            throw new ArgumentException($"Market with id {marketId} not found.");
        }

        var packageHotelAirports = await packageHotelAirportsRepository.ListByMarketIdAndMetaCodes(marketId,
            metaCodes, cancellationToken);
        var arrivalAirports = packageHotelAirports.SelectMany(m => m.Airports).Distinct().ToList();

        if (arrivalAirports.Count == 0)
        {
            return [];
        }

        var flightOccupancy = packageOccupancy.ToFlightSeatsOccupancy();

        var packageFlightVariants = await packageFlightVariantRepository.GetCheapestsPerAirports(
            marketId: marketId,
            stayLengths: stayLengths,
            departureAirports: departureAirports,
            arrivalAirports: arrivalAirports,
            departureDateFrom: departureDateFrom,
            departureDateTo: departureDateTo,
            occupancy: flightOccupancy,
            inboundDepartures: inboundDepartures,
            outboundDepartures: outboundDepartures,
            cancellationToken);

        // take only the cheapest flight variant per arrival airport with the shortest stay length
        packageFlightVariants = packageFlightVariants
            .GroupBy(f => f.Id.ArrivalAirport)
            .ToDictionary(
                g => g.Key,
                g => g
                    .OrderBy(f => f.Id.StayLength)
                    .ThenBy(f => f.Price)
                    .Take(arrivalAirports.Count >= 5 ? 1 : 3)
            )
            .SelectMany(p => p.Value)
            .ToList();
        
        packageFlightVariants = await UpdateFlightVariantPrices(market, packageFlightVariants, packageOccupancy, 
            cancellationToken);
        
        var packageHotelOffers = await GetDynamicPackageHotelOffers(
            market: market,
            occupancies: occupancies,
            packageFlightVariants: packageFlightVariants,
            packageHotelAirports: packageHotelAirports,
            cancellationToken);

        var packageHotelOfferVariants = packageHotelOffers
            .SelectMany(h => h.GetVariants([packageOccupancy], mealPlans))
            .ToList();

        return PackageVariant.CreateManyPerMetaCode(packageFlightVariants, packageHotelOfferVariants,
            packageHotelAirports);
    }

    private async Task<List<PackageHotelOffer>> GetDynamicPackageHotelOffers(
        Market market, List<Occupancy> occupancies, List<PackageFlightVariant> packageFlightVariants,
        List<PackageHotelAirports> packageHotelAirports, CancellationToken cancellationToken)
    {
        var packageHotelOffers = new List<PackageHotelOffer>();

        var mergedOccupancy = occupancies.Merge();
        var packageOccupancy = (PackageOccupancy)PackageOccupancy.MapExact(mergedOccupancy, true)!;

        var airportsToMetaCodes = packageHotelAirports
            .SelectMany(m => m.Airports.Select(a => (ArrivalAirport: a, m.Id.MetaCode)))
            .GroupBy(g => g.ArrivalAirport)
            .ToDictionary(g => g.Key, g => g.Select(x => x.MetaCode).Distinct().ToList());

        var searchCriteriaList = packageFlightVariants
            .Select(f => new HotelSearchCriteria(
                MetaCodes: airportsToMetaCodes.GetValueOrDefault(f.Id.ArrivalAirport, []),
                CheckIn: f.Id.CheckIn,
                CheckOut: f.Id.CheckIn.AddDays(f.Id.StayLength),
                PartnerCode: market.PartnerCode,
                Occupancies: occupancies))
            .Where(c => c.MetaCodes.Count > 0)
            .ToList();

        await Parallel.ForEachAsync(searchCriteriaList, new ParallelOptions
        {
            CancellationToken = cancellationToken
        }, async (criteria, token) =>
        {
            var liveHotelOffers = (await hotelOfferLiveGateway.Search(criteria, token)).ToList();

            var providerConfigurationIds = liveHotelOffers
                .Select(x => x.ProviderConfigurationId)
                .Distinct()
                .ToArray();

            // TODO: Move to domain
            var livePackageHotelOffers = liveHotelOffers
                .GroupBy(x => (x.CheckIn, x.StayLength, x.MetaCode))
                .Select(x => packageHotelOfferFactory.Create(
                    id: new PackageHotelOfferId(
                        checkIn: x.Key.CheckIn,
                        stayLength: x.Key.StayLength,
                        marketId: market.Id,
                        metaCode: x.Key.MetaCode),
                    definitionId: $"{market.Id}-dynamic",
                    currency: market.Currency,
                    occupancies:
                    [packageOccupancy],
                    providerConfigurationIds: providerConfigurationIds,
                    hotelOfferQuotes: x.Select(dto => dto.ToDomainQuote()).ToArray(),
                    airports: [],
                    mergeIntoPackage: false));

            lock (packageHotelOffers)
            {
                packageHotelOffers.AddRange(livePackageHotelOffers);
            }
        });

        return packageHotelOffers;
    }

    private async Task<List<PackageFlightVariant>> UpdateFlightVariantPrices(
        Market market, List<PackageFlightVariant> packageFlightVariants, PackageOccupancy packageOccupancy,
        CancellationToken cancellationToken)
    {
        var packageFlightIds = packageFlightVariants.Select(f => f.GetPackageFlightId()).ToHashSet().ToList();
        var packageFlights = await packageFlightRepository.ListByIds(packageFlightIds, cancellationToken);
        var flightOfferIds = packageFlights.SelectMany(f => f.GetFlightOfferIds()).ToHashSet().ToList();
        
        // TODO: Select firstly cheapest flight offers to reduce the number of flight in the request
        var flightOffers = await flightGateway.GetFlightsByOfferKeyAsync(
            new FlightByOfferKeySearchCriteria(flightOfferIds, packageOccupancy.ToOccupancy(), market.PartnerCode,
                true), cancellationToken);

        // TODO: Move this mapping logic to domain model or factory
        var flightQuotes = flightOffers
            .SelectMany(x => x.Prices)
            .Select(p => new FlightQuote
            {
                FlightId = p.Key,
                Prices = p.Value.PaxConfigurations.Select(price => new
                {
                    Occupancy = new PackageOccupancy(
                        adults: price.Key.Adults,
                        youths: price.Key.Youths,
                        children: price.Key.Children,
                        infants: price.Key.Infants),
                    TotalPrice = price.Value.TotalPrice
                }).ToDictionary(x => x.Occupancy, x => x.TotalPrice),
                Currency = p.Value.Currency,
                UpdateTime = p.Value.RefreshDate
            })
            .DistinctBy(q => q.FlightId)
            .ToDictionary(q => q.FlightId, q => q);

        foreach (var packageFlight in packageFlights)
        {
            packageFlight.AddOccupancy(packageOccupancy);
            packageFlight.ApplyQuotes(flightQuotes);
        }

        return packageFlights.SelectMany(f => f.GetVariants(packageOccupancy)).ToList();
    }

    // TODO: Move this mapping logic from here and from CreatePackagHotelOffersStage to some common place (maybe domain)
    private List<PackageHotelOfferId> GetHotelOfferIds(
        List<PackageFlightVariant> packageFlightVariant,
        List<PackageHotelAirports> packageHotelAirports)
    {
        var airportsToMetaCodes = packageHotelAirports
            .SelectMany(m => m.Airports.Select(a => (ArrivalAirport: a, m.Id.MetaCode)))
            .GroupBy(g => g.ArrivalAirport)
            .ToDictionary(g => g.Key, g => g.Select(x => x.MetaCode).Distinct().ToList());

        return packageFlightVariant
            .SelectMany(f => airportsToMetaCodes.GetValueOrDefault(f.Id.ArrivalAirport, []).Select(metaCode =>
                new PackageHotelOfferId(
                    checkIn: f.Id.CheckIn,
                    stayLength: f.Id.StayLength,
                    marketId: f.Id.MarketId,
                    metaCode: metaCode)))
            .ToList();
    }

    private static PackageVariantDto Map(PackageVariant packageVariant, PackageVariantSourceDto source)
    {
        return new PackageVariantDto(
            PackageId: new PackageId(packageVariant.Id.CheckIn, packageVariant.Id.StayLength,
                packageVariant.Id.MarketId, packageVariant.Id.MetaCode),
            MetaCode: packageVariant.Id.MetaCode,
            StayLength: packageVariant.Id.StayLength,
            CheckIn: packageVariant.Id.CheckIn,
            DepartureAirport: packageVariant.Id.DepartureAirport,
            DepartureDate: packageVariant.DepartureDate,
            ReturnArrivalDate: packageVariant.ReturnArrivalDate,
            MealPlan: packageVariant.Id.MealPlan.ToDto(),
            Price: packageVariant.Price,
            Source: source
        );
    }

    private static bool IsVariantSearchSupported(SearchParameters parameters)
    {
        if (parameters.InboundDepartures.Count > 0 || parameters.OutboundDepartures.Count > 0)
        {
            return false;
        }

        return true;
    }
}