using Esky.Packages.Contract.Packages;
using Esky.Packages.Domain.Events.PackageQuotes;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Model.PackageHotelOffers;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Services;

public interface IPackageService
{
    Task ApplyQuotes(List<PackageQuoteEvent> packageQuotes, CancellationToken cancellationToken);
    Task<PackageDto?> GetById(string packageId, CancellationToken cancellationToken);
    Task<PackageDto[]> GetByIds(string[] packageIds, CancellationToken cancellationToken);
}

internal class PackageService(
    IPackageRepository packageRepository,
    IPackageHotelOfferRepository packageHotelOfferRepository,
    IPackageFlightRepository packageFlightRepository)
    : IPackageService
{
    public async Task ApplyQuotes(List<PackageQuoteEvent> packageQuotes, CancellationToken cancellationToken)
    {
        await packageRepository.ApplyQuotes(packageQuotes, cancellationToken);
    }

    public async Task<PackageDto?> GetById(string packageId, CancellationToken cancellationToken)
    {
        var packages = await GetByIds([packageId], cancellationToken);

        return packages.SingleOrDefault();
    }

    public async Task<PackageDto[]> GetByIds(string[] packageIds, CancellationToken cancellationToken)
    {
        var ids = packageIds
            .Select(id => new PackageId(id))
            .Select(pid => new PackageHotelOfferId(pid.CheckIn, pid.StayLength, pid.MarketId, pid.MetaCode))
            .ToList();
        
        var result = new List<PackageDto>();

        var packageHotelOffers = await packageHotelOfferRepository.ListByIds(ids, cancellationToken);
        foreach (var packageHotelOffer in packageHotelOffers)
        {
            var packageFlights = await GetPackageFlightsForPackageHotelOffer(packageHotelOffer, cancellationToken);
            if (packageFlights.Count == 0)
            {
                continue;
            }

            var packageDto = CreatePackageDto(packageHotelOffer, packageFlights);
            
            // TOOD: Move to dto?
            if (packageDto.FlightOffers.Length == 0 || packageDto.FlightOffers.All(f => f.Prices.Length == 0) || 
                packageDto.HotelOffers.Length == 0 || packageDto.HotelOffers.All(h => h.Prices.Count == 0))
            {
                // Skip packages with no prices
                continue;
            }

            result.Add(packageDto);
        }

        return result.ToArray();
    }

    private static PackageDto CreatePackageDto(
        PackageHotelOffer packageHotelOffer,
        List<PackageFlight> packageFlights)
    {
        var flightOfferPrices = packageFlights
            .Select(packageFlight => new FlightOfferDto(
                ArrivalAirport: packageFlight.Id.ArrivalAirport,
                DepartureAirport: packageFlight.Id.DepartureAirport,
                Prices: packageFlight
                    .GetLowestPricesByOccupancy()
                    .Select(p => new FlightOccupancyPriceEntryDto(
                        Occupancy: p.Key.ToOccupancy().ToDto(), // TODO: Refactor this and take occupancy from request
                        Price: new FlightPriceEntryDto(
                            Id: p.Value.Id,
                            FlightIds: p.Value.FlightIds,
                            Price: p.Value.Price,
                            DepartureDate: p.Value.DepartureDate,
                            ReturnArrivalDate: p.Value.ReturnArrivalDate)))
                    .ToArray()))
            .ToArray();

        var hotelOfferPrices = packageHotelOffer
            .GetLowestPrices()
            .Select(x => new HotelOfferDto(
                Occupancy: x.Key.ToOccupancy().ToDto(),
                Prices: x.Value.ToDictionary(f => f.Key.ToString(), f => f.Value)))
            .ToArray();

        return new PackageDto(
            Id: packageHotelOffer.Id.ToString(),
            CheckIn: packageHotelOffer.Id.CheckIn,
            StayLength: packageHotelOffer.Id.StayLength,
            MetaCode: packageHotelOffer.Id.MetaCode,
            MarketId: packageHotelOffer.Id.MarketId,
            Currency: packageHotelOffer.Currency,
            FlightOffers: flightOfferPrices,
            HotelOffers: hotelOfferPrices);
    }

    private async Task<List<PackageFlight>> GetPackageFlightsForPackageHotelOffer(PackageHotelOffer packageHotelOffer,
        CancellationToken cancellationToken)
    {
<<<<<<< HEAD
        var packageFlightsIds = packageHotelOffer.GetPackageFlightIds();
=======
        var airportPairs = package
            .FlightPricesByOccupancyByDepartureByArrival
            .SelectMany(x => x.Value
                .Select(y => new { ArrivalAirport = x.Key, DepartureAirport = y.Key }))
            .Distinct();

        var packageFlightsIds = airportPairs
            .Select(x => new PackageFlightId(
                checkIn: package.Id.CheckIn,
                stayLength: package.Id.StayLength,
                marketId: package.Id.MarketId,
                arrivalAirport: x.ArrivalAirport,
                departureAirport: x.DepartureAirport))
            .ToList();
>>>>>>> d2098ea (FlightPrices rename proposal, small reformat)

        return await packageFlightRepository
            .ListByIds(packageFlightsIds, cancellationToken);
    }
}