using Esky.Packages.Contract.Packages;
using Esky.Packages.Domain.Events.PackageQuotes;
using Esky.Packages.Domain.Model.PackageFlights;
using Esky.Packages.Domain.Model.Packages;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Application.Services;

public interface IPackageService
{
    Task ApplyQuotes(List<PackageQuoteEvent> packageQuotes, CancellationToken cancellationToken);
    Task<PackageDto?> GetById(string packageId, CancellationToken cancellationToken);
    Task<PackageDto[]> GetByIds(string[] packageIds, CancellationToken cancellationToken);
}

internal class PackageService(
    IPackageRepository packageRepository,
    IPackageFlightRepository packageFlightRepository) 
    : IPackageService
{
    public async Task ApplyQuotes(List<PackageQuoteEvent> packageQuotes, CancellationToken cancellationToken)
    {
        await packageRepository.ApplyQuotes(packageQuotes, cancellationToken);
    }

    public async Task<PackageDto?> GetById(string packageId, CancellationToken cancellationToken)
    {
        var packages = await GetByIds([packageId], cancellationToken);

        return packages.SingleOrDefault();
    }

    public async Task<PackageDto[]> GetByIds(string[] packageIds, CancellationToken cancellationToken)
    {
        var ids = packageIds.Select(MapPackageId).ToList();
        var result = new List<PackageDto>();

        var packages = await packageRepository.ListByIds(ids, cancellationToken);
        foreach (var package in packages)
        {
            var packageFlights = await GetPackageFlightsForPackage(package, cancellationToken);
            if (packageFlights.Count == 0)
            {
                continue;
            }

            var currency = packageFlights.First().Currency;

            result.Add(CreatePackageDto(package, currency, packageFlights));
        }

        return result.ToArray();

        static PackageId MapPackageId(string packageId)
        {
            return PackageId.IsOldPackageId(packageId)
                ? PackageId.ParseFromOldPackageId(packageId)
                : new PackageId(packageId);
        }
    }

    private static PackageDto CreatePackageDto(
        Package package,
        Currency currency,
        List<PackageFlight> packageFlights)
    {
        var flightOfferPrices = packageFlights
            .Select(packageFlight => new FlightOfferDto(
                ArrivalAirport: packageFlight.Id.ArrivalAirport,
                DepartureAirport: packageFlight.Id.DepartureAirport,
                Prices: packageFlight
                    .GetLowestPricesByOccupancy()
                    .Select(p => new FlightOccupancyPriceEntryDto(
                        Occupancy: p.Key.ToOccupancy().ToDto(), // TODO: Refactor this and take occupancy from request
                        Price: new FlightPriceEntryDto(
                            Id: p.Value.Id,
                            FlightIds: p.Value.FlightIds,
                            Price: p.Value.Price,
                            DepartureDate: p.Value.DepartureDate,
                            ReturnArrivalDate: p.Value.ReturnArrivalDate)))
                    .ToArray()))
            .ToArray();

        var hotelOfferPrices = package
            .HotelOfferPrices
            .Select(x => new HotelOfferDto(
                Occupancy: x.Key.ToOccupancy().ToDto(),
                Prices: x.Value.ToDictionary(f => f.Key.ToString(), f => f.Value)))
            .ToArray();

        return new PackageDto(
            Id: package.Id.ToString(),
            CheckIn: package.Id.CheckIn,
            StayLength: package.Id.StayLength,
            MetaCode: package.Id.MetaCode,
            MarketId: package.Id.MarketId,
            Currency: currency,
            FlightOffers: flightOfferPrices,
            HotelOffers: hotelOfferPrices);
    }

    private async Task<List<PackageFlight>> GetPackageFlightsForPackage(
        Package package, 
        CancellationToken cancellationToken)
    {
        var airportPairs = package
            .FlightPricesByOccupancyByDepartureByArrival
            .SelectMany(x => x.Value
                .Select(y => new { ArrivalAirport = x.Key, DepartureAirport = y.Key }))
            .Distinct();

        var packageFlightsIds = airportPairs
            .Select(x => new PackageFlightId(
                checkIn: package.Id.CheckIn,
                stayLength: package.Id.StayLength,
                marketId: package.Id.MarketId,
                arrivalAirport: x.ArrivalAirport,
                departureAirport: x.DepartureAirport))
            .ToList();

        return await packageFlightRepository
            .ListByIds(packageFlightsIds, cancellationToken);
    }
}