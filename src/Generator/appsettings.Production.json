{"Logging": {"ServiceBus": {"Clustermembers": "esky-ets-hotels-pro.rabbitmq-logs-k8s.service.gcp-pro.consul", "UserName": "SECRET", "Password": "SECRET"}}, "HotelGateway": {"ConnectionString": "SECRET"}, "Mongo": {"ConnectionString": "SECRET"}, "Apis": {"FlightCache": "http://esky-flightsearch-api-cache-green.service.gcp-pro.consul", "FlightLive": "http://esky-flightsearch-api-providers-green.service.gcp-pro.consul", "HotelStatic": "http://esky-hotels-static-api-green.service.gcp-pro.consul", "HotelCache": "http://esky-hotels-cache-api.service.gcp-pro.consul", "HotelTransaction": "http://esky-hotels-transaction-api-green.service.gcp-pro.consul", "CurrencyConverter": "http://esky-currencyservice-api-green.service.gcp-pro.consul"}, "BloomFilterNotificationProducer": {"BootstrapServers": "esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-0.service.gcp-pro.consul:9092,esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-1.service.gcp-pro.consul:9092,esky-ets-hotels-pro.kafka-hotels-pro-kafka-node-2.service.gcp-pro.consul:9092", "Topic": "bloomFilterNotifications", "CompressionType": "Lz4"}, "BigQuery": {"ProjectId": "esky-ets-logs-pro", "Dataset": "Packages"}}