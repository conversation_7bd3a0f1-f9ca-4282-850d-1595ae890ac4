using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Repositories;
using Esky.Packages.Domain.Types;
using Esky.Packages.Infrastructure.Database;
using Esky.Packages.Infrastructure.Database.IndexInitializers;
using Esky.Packages.Infrastructure.Database.Projections;
using MongoDB.Bson;
using MongoDB.Driver;

namespace Esky.Packages.Infrastructure.Repositories;

internal class PackageFlightVariantRepository(PackageDatabase database)
    : IPackageFlightVariantRepository, IIndexInitializer
{
    private const string CollectionName = "packageFlightVariants";
    
    private const int MaxStayLengths = 3;
    private const int MaxVariantsPerArrivalAirport = 20;

    private readonly IMongoCollection<PackageFlightVariant> _packageFlightVariants =
        database.Database.GetCollection<PackageFlightVariant>(CollectionName);

    public async Task UpdateVariants(List<PackageFlightVariantEvent> events, CancellationToken cancellationToken)
    {
        var updates = new List<WriteModel<PackageFlightVariant>>();

        var f = Builders<PackageFlightVariant>.Filter;

        var uniqueEvents = events
            .GroupBy(e => new PackageFlightVariantId(
                checkIn: e.CheckIn,
                stayLength: e.StayLength,
                marketId: e.MarketId,
                arrivalAirport: e.ArrivalAirport,
                departureAirport: e.DepartureAirport,
                occupancy: e.Occupancy,
                inboundDeparture: e.InboundDeparture,
                outboundDeparture: e.OutboundDeparture))
            .ToDictionary(g => g.Key, g => g.Last())
            .Select(g => g.Value)
            .ToList();

        foreach (var calendarEvent in uniqueEvents)
        {
            if (calendarEvent is PackageFlightVariantUpdatedEvent updatedEvent)
            {
                var search = PackageFlightVariant.Create(
                    checkIn: updatedEvent.CheckIn,
                    stayLength: updatedEvent.StayLength,
                    marketId: updatedEvent.MarketId,
                    packageOccupancy: updatedEvent.Occupancy,
                    arrivalAirport: updatedEvent.ArrivalAirport,
                    departureAirport: updatedEvent.DepartureAirport,
                    inboundDeparture: updatedEvent.InboundDeparture,
                    outboundDeparture: updatedEvent.OutboundDeparture,
                    departureDate: updatedEvent.DepartureDate,
                    returnArrivalDate: updatedEvent.ReturnArrivalDate,
                    price: updatedEvent.Price);

                var filter = f.Eq(x => x.Id, search.Id);

                var update = new BsonDocument
                {
                    {
                        "$set", new BsonDocument
                        {
                            // TODO: Use property names from mapping instead of hardcoded values
                            { "d", search.DepartureDate.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc) },
                            { "ra", search.ReturnArrivalDate.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc) },
                            { "p", search.Price }
                        }
                    }
                };

                updates.Add(new UpdateOneModel<PackageFlightVariant>(filter, update) { IsUpsert = true });
            }
            else if (calendarEvent is PackageFlightVariantDeletedEvent deletedEvent)
            {
                var id = new PackageFlightVariantId(
                    checkIn: deletedEvent.CheckIn,
                    stayLength: deletedEvent.StayLength,
                    marketId: deletedEvent.MarketId,
                    arrivalAirport: deletedEvent.ArrivalAirport,
                    departureAirport: deletedEvent.DepartureAirport,
                    occupancy: deletedEvent.Occupancy,
                    inboundDeparture: deletedEvent.InboundDeparture,
                    outboundDeparture: deletedEvent.OutboundDeparture);

                var filter = f.Eq(x => x.Id, id);

                updates.Add(new DeleteOneModel<PackageFlightVariant>(filter));
            }
            else
            {
                throw new NotSupportedException($"Event type {calendarEvent.GetType().Name} is not supported.");
            }
        }

        if (updates.Count > 0)
        {
            await _packageFlightVariants.BulkWriteAsync(updates, options: new BulkWriteOptions
            {
                IsOrdered = false
            }, cancellationToken: cancellationToken);
        }
    }

    public async Task<List<PackageFlightVariant>> GetCheapestsPerDates(string marketId, List<Airport> departureAirports,
        List<Airport> arrivalAirports, PackageOccupancy occupancy, List<TimeOfDay> inboundDepartures, 
        List<TimeOfDay> outboundDepartures, CancellationToken cancellationToken = default)
    {
        var match = new BsonDocument("$match",
            new BsonDocument
            {
                { "_id.o", occupancy.ToString() },
                { "_id.k", marketId },
                {
                    "_id.a", new BsonDocument("$in", new BsonArray(arrivalAirports.Select(a => a.ToString())))
                }
            });

        if (departureAirports.Count > 0)
        {
            match["$match"]["_id.d"] = new BsonDocument
            {
                { "$in", new BsonArray(departureAirports.Select(a => a.ToString())) }
            };
        }
        
        if (inboundDepartures.Count > 0)
        {
            match["$match"]["_id.id"] = new BsonDocument("$in",
                new BsonArray(inboundDepartures.Select(td => td.ToShortString())));
        }
        
        if (outboundDepartures.Count > 0)
        {
            match["$match"]["_id.od"] = new BsonDocument("$in",
                new BsonArray(outboundDepartures.Select(td => td.ToShortString())));
        }

        var pipeline = new[]
        {
            match,
            new("$group",
                new BsonDocument
                {
                    {
                        "_id", new BsonDocument
                        {
                            { "d", "$d" },
                            { "s", "$_id.s" },
                        }
                    },
                    {
                        "c",
                        new BsonDocument("$min", new BsonDocument
                        {
                            { "p", "$p" },
                            { "ra", "$ra" },
                            { "d", "$_id.d" },
                            { "a", "$_id.a" },
                            { "c", "$_id.c" },
                            { "id", "$_id.id" },
                            { "od", "$_id.od" }
                        })
                    }
                }),
            new("$project", new BsonDocument
            {
                { "d", "$c.d" },
                { "a", "$c.a" },
                { "s", "$_id.s" },
                { "c", "$c.c" },
                { "dd", "$_id.d" },
                { "rad", "$c.ra" },
                { "id", "$c.id" },
                { "od", "$c.od" },
                { "p", "$c.p" },
                { "_id", 0 }
            })
        };

        var cursor = await _packageFlightVariants.AggregateAsync<PackageFlightVariantProjection>(pipeline,
            cancellationToken: cancellationToken);
        var result = await cursor.ToListAsync(cancellationToken);

        return result
            .Select(x => PackageFlightVariant.Create(
                checkIn: x.CheckIn,
                stayLength: x.StayLength,
                marketId: marketId,
                packageOccupancy: occupancy,
                departureAirport: x.DepartureAirport,
                arrivalAirport: x.ArrivalAirport,
                inboundDeparture: x.InboundDeparture,
                outboundDeparture: x.OutboundDeparture,
                departureDate: x.DepartureDate,
                returnArrivalDate: x.ReturnArrivalDate,
                price: x.Price))
            .ToList();
    }

    public async Task<List<PackageFlightVariant>> GetCheapestsPerAirports(string marketId, List<int> stayLengths,
        List<Airport> departureAirports, List<Airport> arrivalAirports, DateOnly departureDateFrom,
        DateOnly departureDateTo, PackageOccupancy occupancy, List<TimeOfDay> inboundDepartures, 
        List<TimeOfDay> outboundDepartures, CancellationToken cancellationToken)
    {
        var departureDateTimeFrom = departureDateFrom.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);
        var departureDateTimeTo = departureDateTo.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);

        if (stayLengths.Count > MaxStayLengths)
        {
            stayLengths = stayLengths.OrderBy(x => x).Take(MaxStayLengths).ToList();
        }

        var days = (departureDateTimeTo - departureDateTimeFrom).TotalDays + 1;
        var limit = Math.Min(days, MaxVariantsPerArrivalAirport);

        var match = new BsonDocument("$match",
            new BsonDocument
            {
                { "_id.o", occupancy.ToString() },
                { "_id.k", marketId },
                {
                    "_id.s", new BsonDocument("$in", new BsonArray(stayLengths))
                },
                {
                    "_id.a", new BsonDocument("$in", new BsonArray(arrivalAirports.Select(a => a.ToString())))
                },
                {
                    "$and",
                    new BsonArray
                    {
                        new BsonDocument("d", new BsonDocument("$gte", departureDateTimeFrom)),
                        new BsonDocument("d", new BsonDocument("$lte", departureDateTimeTo)),
                    }
                }
            });

        if (departureAirports.Count > 0)
        {
            match["$match"]["_id.d"] = new BsonDocument
            {
                { "$in", new BsonArray(departureAirports.Select(a => a.ToString())) }
            };
        }
        
        if (inboundDepartures.Count > 0)
        {
            match["$match"]["_id.id"] = new BsonDocument("$in",
                new BsonArray(inboundDepartures.Select(td => td.ToShortString())));
        }
        
        if (outboundDepartures.Count > 0)
        {
            match["$match"]["_id.od"] = new BsonDocument("$in",
                new BsonArray(outboundDepartures.Select(td => td.ToShortString())));
        }

        var pipeline = new[]
        {
            match,
            new("$group",
                new BsonDocument
                {
                    {
                        "_id", new BsonDocument
                        {
                            { "a", "$_id.a" },
                            { "d", "$d" },
                            { "s", "$_id.s" },
                        }
                    },
                    {
                        "f",
                        new BsonDocument("$min", new BsonDocument
                        {
                            { "p", "$p" },
                            { "rad", "$ra" },
                            { "s", "$_id.s" },
                            { "d", "$_id.d" },
                            { "c", "$_id.c" },
                            { "id", "$_id.id" },
                            { "od", "$_id.od" }
                        })
                    }
                }),
            new("$group",
                new BsonDocument
                {
                    {
                        "_id", new BsonDocument
                        {
                            { "a", "$_id.a" },
                            { "s", "$_id.s" },
                        }
                    },
                    {
                        "f",
                        new BsonDocument("$minN", new BsonDocument
                        {
                            { "n", limit },
                            {
                                "input", new BsonDocument
                                {
                                    { "p", "$f.p" },
                                    { "dd", "$_id.d" },
                                    { "rad", "$f.rad" },
                                    { "s", "$_id.s" },
                                    { "d", "$f.d" },
                                    { "c", "$f.c" },
                                    { "id", "$f.id" },
                                    { "od", "$f.od" }
                                }
                            }
                        })
                    }
                }),
            new("$unwind", "$f"),
            new("$project", new BsonDocument
            {
                { "a", "$_id.a" },
                { "d", "$f.d" },
                { "s", "$_id.s" },
                { "c", "$f.c" },
                { "dd", "$f.dd" },
                { "rad", "$f.rad" },
                { "id", "$f.id" },
                { "od", "$f.od" },
                { "p", "$f.p" },
                { "_id", 0 }
            })
        };

        var cursor = await _packageFlightVariants.AggregateAsync<PackageFlightVariantProjection>(pipeline,
            cancellationToken: cancellationToken);
        var result = await cursor.ToListAsync(cancellationToken);

        return result
            .Select(x => PackageFlightVariant.Create(
                checkIn: x.CheckIn,
                stayLength: x.StayLength,
                marketId: marketId,
                packageOccupancy: occupancy,
                arrivalAirport: x.ArrivalAirport,
                departureAirport: x.DepartureAirport,
                inboundDeparture: x.InboundDeparture,
                outboundDeparture: x.OutboundDeparture,
                departureDate: x.DepartureDate,
                returnArrivalDate: x.ReturnArrivalDate,
                price: x.Price))
            .ToList();
    }

    public async Task<List<Airport>> GetDepartureAirports(string marketId, List<Airport> arrivalAirports,
        PackageOccupancy occupancy, CancellationToken cancellationToken = default)
    {
        var match = new BsonDocument("$match",
            new BsonDocument
            {
                { "_id.o", occupancy.ToString() },
                { "_id.k", marketId },
                { "_id.a", new BsonDocument("$in", new BsonArray(arrivalAirports.Select(a => a.ToString()))) }
            });

        var pipeline = new[]
        {
            match,
            new("$group",
                new BsonDocument
                {
                    { "_id", "$_id.d" },
                }),
            new("$project", new BsonDocument
            {
                { "d", "$_id" },
                { "_id", 0 }
            })
        };

        var cursor = await _packageFlightVariants.AggregateAsync<PackageFlightDepartureAirportsProjection>(pipeline,
            cancellationToken: cancellationToken);
        var result = await cursor.ToListAsync(cancellationToken);

        return result
            .Select(x => x.DepartureAirport)
            .ToList();
    }

    public async Task EnsureIndexes()
    {
        await database.Database.CreateCollectionAsync(CollectionName);

        await _packageFlightVariants.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageFlightVariant>(
                Builders<PackageFlightVariant>.IndexKeys
                    .Ascending(x => x.Id.ArrivalAirport) // grouped by meta codes
                    .Ascending(x => x.Id.MarketId)
                    .Ascending(x => x.Id.Occupancy)
                    .Ascending(x => x.Price)
                    .Ascending(x => x.Id.StayLength) // one or more? can it be always one?
                    .Ascending(x => x.Id.DepartureAirport)
                    .Ascending(x => x.DepartureDate) // whole year (360) or more
                    .Ascending(x => x.Id.InboundDeparture)
                    .Ascending(x => x.Id.OutboundDeparture)
            )
        );

        // TODO: Add index for Inbound/Outbound times when needed

        // for sharding purposes
        await _packageFlightVariants.Indexes.CreateOneAsync(
            new CreateIndexModel<PackageFlightVariant>(
                Builders<PackageFlightVariant>.IndexKeys
                    .Hashed(x => x.Id.ArrivalAirport)
            )
        );
    }
}