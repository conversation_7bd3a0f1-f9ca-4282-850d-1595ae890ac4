using Esky.Packages.Domain.Events.PackageFlightVariants;
using Esky.Packages.Domain.Model.Common;
using Esky.Packages.Domain.Model.PackageFlightVariants;
using Esky.Packages.Domain.Types;

namespace Esky.Packages.Domain.Repositories;

public interface IPackageFlightVariantRepository
{
    Task UpdateVariants(List<PackageFlightVariantEvent> events, CancellationToken cancellationToken = default);
    
    Task<List<PackageFlightVariant>> GetCheapestsPerAirports(string marketId, List<int> stayLengths, 
        List<Airport> departureAirports, List<Airport> arrivalAirports, DateOnly departureDateFrom, 
        DateOnly departureDateTo, PackageOccupancy occupancy, List<TimeOfDay> inboundDepartures, 
        List<TimeOfDay> outboundDepartures, CancellationToken cancellationToken = default);

    Task<List<PackageFlightVariant>> GetCheapestsPerDates(string marketId, List<Airport> departureAirports,
        List<Airport> arrivalAirports, PackageOccupancy occupancy, List<TimeOfDay> inboundDepartures, 
        List<TimeOfDay> outboundDepartures, CancellationToken cancellationToken = default);
    
    Task<List<Airport>> GetDepartureAirports(string marketId, List<Airport> arrivalAirports, PackageOccupancy occupancy,
        CancellationToken cancellationToken = default);
}