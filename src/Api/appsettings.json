{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}, "ServiceBus": {"ApplicationName": "esky-packages-api", "Clustermembers": "esky-ets-hotels-ci.rabbitmq-logs-k8s.service.gcp-staging.consul", "Vhost": "ets", "UserName": "SECRET", "Password": "SECRET"}}, "HotelGateway": {"ConnectionString": "SECRET"}, "Apis": {"FlightCache": "http://esky-flightsearch-api-cache-green.service.gcp-pro.consul", "FlightLive": "http://esky-flightsearch-api-providers-green.service.gcp-pro.consul", "HotelStatic": "http://esky-hotels-static-api-green.service.gcp-pro.consul", "HotelCache": "http://esky-hotels-cache-api.service.gcp-pro.consul", "HotelTransaction": "http://esky-hotels-transaction-api-green.service.gcp-pro.consul", "HotelApi": "http://esky-hotels-api-green.service.gcp-pro.consul", "CurrencyConverter": "http://esky-currencyservice-api-green.service.gcp-pro.consul", "OfferAccuracy": {"Url": "http://esky-offer-accuracy-monitor.service.gcp-pro.consul", "TimeoutInMiliseconds": 1000}}}